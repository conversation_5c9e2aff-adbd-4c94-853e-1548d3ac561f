package mux

import (
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestStaticFile(t *testing.T) {
	// Create a temporary file for testing
	tmpDir := t.TempDir()
	testFile := filepath.Join(tmpDir, "test.txt")
	testContent := "Hello, World!"

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatal(err)
	}

	engine := New()
	engine.StaticFile("/test.txt", testFile)

	// Test that the route is registered
	handler := engine.findHandler("GET", "/test.txt")
	if handler == nil {
		t.Fatal("StaticFile route should be registered")
	}

	// Create a mock request
	req, err := http.NewRequest("GET", "/test.txt", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)
	handler(ctx)

	// Check response
	if ctx.response.statusCode != 200 {
		t.Errorf("Expected status 200, got %d", ctx.response.statusCode)
	}

	if string(ctx.response.body) != testContent {
		t.Errorf("Expected body '%s', got '%s'", testContent, string(ctx.response.body))
	}

	// Check content type
	contentType := ctx.response.headers["Content-Type"]
	if !strings.Contains(contentType, "text/plain") {
		t.Errorf("Expected text/plain content type, got '%s'", contentType)
	}
}

func TestStaticFileNotFound(t *testing.T) {
	engine := New()
	engine.StaticFile("/notfound.txt", "/nonexistent/file.txt")

	handler := engine.findHandler("GET", "/notfound.txt")
	if handler == nil {
		t.Fatal("StaticFile route should be registered")
	}

	req, err := http.NewRequest("GET", "/notfound.txt", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)
	handler(ctx)

	// Should return 404
	if ctx.response.statusCode != 404 {
		t.Errorf("Expected status 404, got %d", ctx.response.statusCode)
	}
}

func TestStatic(t *testing.T) {
	// Create a temporary directory with test files
	tmpDir := t.TempDir()

	// Create test files
	testFile1 := filepath.Join(tmpDir, "file1.txt")
	testFile2 := filepath.Join(tmpDir, "file2.html")
	subDir := filepath.Join(tmpDir, "subdir")

	err := os.Mkdir(subDir, 0755)
	if err != nil {
		t.Fatal(err)
	}

	testFile3 := filepath.Join(subDir, "file3.css")

	err = os.WriteFile(testFile1, []byte("File 1 content"), 0644)
	if err != nil {
		t.Fatal(err)
	}

	err = os.WriteFile(testFile2, []byte("<html>File 2</html>"), 0644)
	if err != nil {
		t.Fatal(err)
	}

	err = os.WriteFile(testFile3, []byte("body { color: red; }"), 0644)
	if err != nil {
		t.Fatal(err)
	}

	engine := New()
	engine.Static("/assets", tmpDir)

	// Test that the wildcard route pattern is registered
	handler := engine.findHandler("GET", "/assets/file1.txt")
	if handler == nil {
		t.Fatal("Static route should be registered")
	}

	// Test that the handler can actually serve the file
	req, err := http.NewRequest("GET", "/assets/file1.txt", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)
	handler, params := engine.findHandlerWithParams("GET", "/assets/file1.txt")
	if handler == nil {
		t.Fatal("Static handler should be found")
	}

	// Set the filepath parameter
	for key, value := range params {
		ctx.params[key] = value
	}

	// Execute the handler
	handler(ctx)

	// Check that we got a response (either file content or 404)
	if ctx.response.statusCode != 200 && ctx.response.statusCode != 404 {
		t.Errorf("Expected status 200 or 404, got %d", ctx.response.statusCode)
	}
}

func TestStaticFS(t *testing.T) {
	// Create a temporary directory with test files
	tmpDir := t.TempDir()
	testFile := filepath.Join(tmpDir, "test.js")
	testContent := "console.log('Hello, World!');"

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatal(err)
	}

	engine := New()
	engine.StaticFS("/static", http.Dir(tmpDir))

	// Test that the route pattern is registered
	handler := engine.findHandler("GET", "/static/test.js")
	if handler == nil {
		t.Fatal("StaticFS route should be registered")
	}

	// Test that the handler can actually serve the file
	req, err := http.NewRequest("GET", "/static/test.js", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)
	handler, params := engine.findHandlerWithParams("GET", "/static/test.js")
	if handler == nil {
		t.Fatal("StaticFS handler should be found")
	}

	// Set the filepath parameter
	for key, value := range params {
		ctx.params[key] = value
	}

	// Execute the handler
	handler(ctx)

	// Check that we got a response (either file content or 404)
	if ctx.response.statusCode != 200 && ctx.response.statusCode != 404 {
		t.Errorf("Expected status 200 or 404, got %d", ctx.response.statusCode)
	}
}

func TestStaticPanicOnInvalidPath(t *testing.T) {
	engine := New()

	// Test that Static panics with URL parameters
	defer func() {
		if r := recover(); r == nil {
			t.Error("Static should panic with URL parameters")
		}
	}()

	engine.Static("/assets/:id", "./assets")
}

func TestStaticFilePanicOnInvalidPath(t *testing.T) {
	engine := New()

	// Test that StaticFile panics with URL parameters
	defer func() {
		if r := recover(); r == nil {
			t.Error("StaticFile should panic with URL parameters")
		}
	}()

	engine.StaticFile("/file/:id", "./file.txt")
}

func TestFileMethod(t *testing.T) {
	// Create a temporary file for testing
	tmpDir := t.TempDir()
	testFile := filepath.Join(tmpDir, "test.json")
	testContent := `{"message": "Hello, World!"}`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatal(err)
	}

	engine := New()
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)
	ctx.File(testFile)

	// Check response
	if ctx.response.statusCode != 200 {
		t.Errorf("Expected status 200, got %d", ctx.response.statusCode)
	}

	if string(ctx.response.body) != testContent {
		t.Errorf("Expected body '%s', got '%s'", testContent, string(ctx.response.body))
	}

	// Check content type for JSON file
	contentType := ctx.response.headers["Content-Type"]
	if !strings.Contains(contentType, "application/json") {
		t.Errorf("Expected JSON content type, got '%s'", contentType)
	}

	// Check that cache headers are set
	cacheControl := ctx.response.headers["Cache-Control"]
	if cacheControl == "" {
		t.Error("Cache-Control header should be set")
	}

	lastModified := ctx.response.headers["Last-Modified"]
	if lastModified == "" {
		t.Error("Last-Modified header should be set")
	}
}

func TestFileMethodNotFound(t *testing.T) {
	engine := New()
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)
	ctx.File("/nonexistent/file.txt")

	// Should return 404
	if ctx.response.statusCode != 404 {
		t.Errorf("Expected status 404, got %d", ctx.response.statusCode)
	}
}

func TestFileMethodDirectory(t *testing.T) {
	tmpDir := t.TempDir()

	engine := New()
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := engine.createContext(nil, req)
	ctx.File(tmpDir) // Try to serve a directory

	// Should return 403 Forbidden
	if ctx.response.statusCode != 403 {
		t.Errorf("Expected status 403, got %d", ctx.response.statusCode)
	}
}

func TestMimeTypeDetection(t *testing.T) {
	// Create temporary files with different extensions
	tmpDir := t.TempDir()

	testCases := []struct {
		filename    string
		content     string
		expectedType string
	}{
		{"test.html", "<html></html>", "text/html"},
		{"test.css", "body { color: red; }", "text/css"},
		{"test.js", "console.log('test');", "text/javascript"},
		{"test.json", `{"test": true}`, "application/json"},
		{"test.png", "\x89PNG\r\n\x1a\n", "image/png"},
	}

	engine := New()

	for _, tc := range testCases {
		testFile := filepath.Join(tmpDir, tc.filename)
		err := os.WriteFile(testFile, []byte(tc.content), 0644)
		if err != nil {
			t.Fatal(err)
		}

		req, err := http.NewRequest("GET", "/test", nil)
		if err != nil {
			t.Fatal(err)
		}

		ctx := engine.createContext(nil, req)
		ctx.File(testFile)

		contentType := ctx.response.headers["Content-Type"]
		if !strings.Contains(contentType, tc.expectedType) {
			t.Errorf("For file %s, expected content type to contain '%s', got '%s'",
				tc.filename, tc.expectedType, contentType)
		}
	}
}
