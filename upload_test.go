package mux

import (
	"bytes"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestFormFile(t *testing.T) {
	// Create a test engine
	engine := New()
	engine.MaxMultipartMemory = 8 << 20 // 8 MiB

	// Create a test route
	engine.POST("/upload", func(c *Context) {
		file, err := c.FormFile("file")
		if err != nil {
			c.Status(400).String("Error: %s", err.Error())
			return
		}
		c.Status(200).String("File: %s, Size: %d", file.Filename, file.Size)
	})

	// Create a multipart form with a file
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add a file field
	fileWriter, err := writer.CreateFormFile("file", "test.txt")
	if err != nil {
		t.Fatal(err)
	}
	fileContent := "Hello, World!"
	fileWriter.Write([]byte(fileContent))
	writer.Close()

	// Create a request
	req, err := http.NewRequest("POST", "/upload", body)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Create a context
	ctx := engine.createContext(nil, req)

	// Test FormFile
	file, err := ctx.FormFile("file")
	if err != nil {
		t.Fatalf("FormFile failed: %v", err)
	}

	if file.Filename != "test.txt" {
		t.Errorf("Expected filename 'test.txt', got '%s'", file.Filename)
	}

	if file.Size != int64(len(fileContent)) {
		t.Errorf("Expected file size %d, got %d", len(fileContent), file.Size)
	}
}

func TestMultipartForm(t *testing.T) {
	// Create a test engine
	engine := New()

	// Create a test route
	engine.POST("/upload", func(c *Context) {
		form, err := c.MultipartForm()
		if err != nil {
			c.Status(400).String("Error: %s", err.Error())
			return
		}
		files := form.File["files"]
		c.Status(200).String("Files count: %d", len(files))
	})

	// Create a multipart form with multiple files
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add multiple files
	for i, filename := range []string{"file1.txt", "file2.txt"} {
		fileWriter, err := writer.CreateFormFile("files", filename)
		if err != nil {
			t.Fatal(err)
		}
		fileWriter.Write([]byte("Content " + string(rune('1'+i))))
	}
	writer.Close()

	// Create a request
	req, err := http.NewRequest("POST", "/upload", body)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Create a context
	ctx := engine.createContext(nil, req)

	// Test MultipartForm
	form, err := ctx.MultipartForm()
	if err != nil {
		t.Fatalf("MultipartForm failed: %v", err)
	}

	files := form.File["files"]
	if len(files) != 2 {
		t.Errorf("Expected 2 files, got %d", len(files))
	}

	expectedFilenames := []string{"file1.txt", "file2.txt"}
	for i, file := range files {
		if file.Filename != expectedFilenames[i] {
			t.Errorf("Expected filename '%s', got '%s'", expectedFilenames[i], file.Filename)
		}
	}
}

func TestSaveUploadedFile(t *testing.T) {
	// Create a test engine
	engine := New()

	// Create a temporary directory for testing
	tempDir := t.TempDir()

	// Create a multipart form with a file
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	fileWriter, err := writer.CreateFormFile("file", "test.txt")
	if err != nil {
		t.Fatal(err)
	}
	fileContent := "Hello, World! This is a test file."
	fileWriter.Write([]byte(fileContent))
	writer.Close()

	// Create a request
	req, err := http.NewRequest("POST", "/upload", body)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Create a context
	ctx := engine.createContext(nil, req)

	// Get the file
	file, err := ctx.FormFile("file")
	if err != nil {
		t.Fatalf("FormFile failed: %v", err)
	}

	// Save the file
	dst := filepath.Join(tempDir, "saved_test.txt")
	err = ctx.SaveUploadedFile(file, dst)
	if err != nil {
		t.Fatalf("SaveUploadedFile failed: %v", err)
	}

	// Verify the file was saved correctly
	savedContent, err := os.ReadFile(dst)
	if err != nil {
		t.Fatalf("Failed to read saved file: %v", err)
	}

	if string(savedContent) != fileContent {
		t.Errorf("Expected content '%s', got '%s'", fileContent, string(savedContent))
	}
}

func TestSaveUploadedFileWithSubdirectory(t *testing.T) {
	// Create a test engine
	engine := New()

	// Create a temporary directory for testing
	tempDir := t.TempDir()

	// Create a multipart form with a file
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	fileWriter, err := writer.CreateFormFile("file", "test.txt")
	if err != nil {
		t.Fatal(err)
	}
	fileContent := "Test content for subdirectory"
	fileWriter.Write([]byte(fileContent))
	writer.Close()

	// Create a request
	req, err := http.NewRequest("POST", "/upload", body)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Create a context
	ctx := engine.createContext(nil, req)

	// Get the file
	file, err := ctx.FormFile("file")
	if err != nil {
		t.Fatalf("FormFile failed: %v", err)
	}

	// Save the file in a subdirectory (should create the directory)
	dst := filepath.Join(tempDir, "uploads", "subdir", "saved_test.txt")
	err = ctx.SaveUploadedFile(file, dst)
	if err != nil {
		t.Fatalf("SaveUploadedFile failed: %v", err)
	}

	// Verify the file was saved correctly
	savedContent, err := os.ReadFile(dst)
	if err != nil {
		t.Fatalf("Failed to read saved file: %v", err)
	}

	if string(savedContent) != fileContent {
		t.Errorf("Expected content '%s', got '%s'", fileContent, string(savedContent))
	}

	// Verify the directory was created
	if _, err := os.Stat(filepath.Dir(dst)); os.IsNotExist(err) {
		t.Error("Expected directory to be created")
	}
}

func TestMaxMultipartMemory(t *testing.T) {
	// Create a test engine with small memory limit
	engine := New()
	engine.MaxMultipartMemory = 1024 // 1 KB

	// Create a large file (larger than memory limit)
	largeContent := strings.Repeat("A", 2048) // 2 KB

	// Create a multipart form with a large file
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	fileWriter, err := writer.CreateFormFile("file", "large.txt")
	if err != nil {
		t.Fatal(err)
	}
	fileWriter.Write([]byte(largeContent))
	writer.Close()

	// Create a request
	req, err := http.NewRequest("POST", "/upload", body)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Create a context
	ctx := engine.createContext(nil, req)

	// This should still work as Go's multipart parser handles large files by writing to temp files
	file, err := ctx.FormFile("file")
	if err != nil {
		t.Fatalf("FormFile failed: %v", err)
	}

	if file.Filename != "large.txt" {
		t.Errorf("Expected filename 'large.txt', got '%s'", file.Filename)
	}

	if file.Size != int64(len(largeContent)) {
		t.Errorf("Expected file size %d, got %d", len(largeContent), file.Size)
	}
}
