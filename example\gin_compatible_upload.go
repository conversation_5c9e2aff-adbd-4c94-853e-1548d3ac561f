//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"
	"os"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

func main() {
	router := mux.Default()
	
	// Set a lower memory limit for multipart forms (default is 32 MiB)
	router.MaxMultipartMemory = 8 << 20 // 8 MiB

	// Create upload directory
	if err := os.MkdirAll("./files", 0755); err != nil {
		log.Fatal("Failed to create upload directory:", err)
	}

	// Multiple files upload (exactly like Gin example)
	router.POST("/upload/multiple", func(c *mux.Context) {
		// Multipart form
		form, _ := c.MultipartForm()
		files := form.File["files"]

		for _, file := range files {
			log.Println(file.Filename)

			// Upload the file to specific dst.
			c.SaveUploadedFile(file, "./files/"+file.Filename)
		}
		c.Status(http.StatusOK).String(fmt.Sprintf("%d files uploaded!", len(files)))
	})

	// Single file upload (exactly like Gin example)
	router.POST("/upload", func(c *mux.Context) {
		// single file
		file, _ := c.FormFile("file")
		log.Println(file.Filename)

		// Upload the file to specific dst.
		c.SaveUploadedFile(file, "./files/"+file.Filename)

		c.Status(http.StatusOK).String(fmt.Sprintf("'%s' uploaded!", file.Filename))
	})

	// Test form page
	router.GET("/", func(c *mux.Context) {
		c.HTML(`
<!DOCTYPE html>
<html>
<head>
    <title>Gin-Compatible File Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .section { margin-bottom: 40px; padding: 20px; border: 1px solid #eee; }
        h2 { color: #333; }
        .code { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gin-Compatible File Upload Demo</h1>
        <p>This demo shows file upload functionality that exactly matches Gin framework patterns.</p>
        
        <div class="section">
            <h2>Single File Upload</h2>
            <div class="code">
// Single file (exactly like Gin)<br>
file, _ := c.FormFile("file")<br>
log.Println(file.Filename)<br>
c.SaveUploadedFile(file, "./files/" + file.Filename)<br>
c.String(http.StatusOK, fmt.Sprintf("'%s' uploaded!", file.Filename))
            </div>
            <form action="/upload" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="file">Choose file:</label>
                    <input type="file" name="file" id="file" required>
                </div>
                <button type="submit">Upload File</button>
            </form>
        </div>

        <div class="section">
            <h2>Multiple Files Upload</h2>
            <div class="code">
// Multipart form (exactly like Gin)<br>
form, _ := c.MultipartForm()<br>
files := form.File["files"]<br>
for _, file := range files {<br>
&nbsp;&nbsp;log.Println(file.Filename)<br>
&nbsp;&nbsp;c.SaveUploadedFile(file, "./files/" + file.Filename)<br>
}<br>
c.String(http.StatusOK, fmt.Sprintf("%d files uploaded!", len(files)))
            </div>
            <form action="/upload/multiple" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="files">Choose files:</label>
                    <input type="file" name="files" id="files" multiple required>
                </div>
                <button type="submit">Upload Files</button>
            </form>
        </div>

        <div class="section">
            <h2>Configuration</h2>
            <div class="code">
// Set memory limit (exactly like Gin)<br>
router.MaxMultipartMemory = 8 << 20  // 8 MiB
            </div>
            <p>Current MaxMultipartMemory: 8 MiB</p>
        </div>

        <div class="section">
            <h2>Uploaded Files</h2>
            <p><a href="/files">View uploaded files</a></p>
        </div>
    </div>
</body>
</html>`)
	})

	// Serve uploaded files
	router.Static("/files", "./files")

	log.Println("Starting Gin-compatible file upload demo on :8080")
	log.Println("Visit http://localhost:8080 to test uploads")
	log.Println("MaxMultipartMemory:", router.MaxMultipartMemory/(1<<20), "MiB")

	if err := router.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
