package mux

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"log"
	"mime"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/panjf2000/gnet/v2"
	"gopkg.in/yaml.v3"
	"google.golang.org/protobuf/proto"
)

// HandlerFunc defines the handler function type
type HandlerFunc func(*Context)

// Middleware defines the middleware function type
type Middleware func(HandlerFunc) HandlerFunc

// H is a shortcut for map[string]interface{}
type H map[string]interface{}

// Context represents the context of the current HTTP request
type Context struct {
	conn     gnet.Conn
	request  *http.Request
	response *Response
	params   map[string]string
	keys     map[string]interface{}
	mu       sync.RWMutex
	index    int
	handlers []HandlerFunc
	engine   *Engine // reference to the engine for accessing JSON encoder/decoder
}

// Response represents the HTTP response
type Response struct {
	headers    map[string]string
	statusCode int
	body       []byte
	written    bool
}

// Config represents the configuration for the Engine
type Config struct {
	// JSONEncoder is the function used to encode JSON responses
	// If not set, encoding/json.Marshal will be used
	JSONEncoder func(v interface{}) ([]byte, error)

	// JSONDecoder is the function used to decode JSON requests
	// If not set, encoding/json.Unmarshal will be used
	JSONDecoder func(data []byte, v interface{}) error

	// XMLEncoder is the function used to encode XML responses
	// If not set, encoding/xml.Marshal will be used
	XMLEncoder func(v interface{}) ([]byte, error)

	// YAMLEncoder is the function used to encode YAML responses
	// If not set, gopkg.in/yaml.v3.Marshal will be used
	YAMLEncoder func(v interface{}) ([]byte, error)

	// ProtoBufEncoder is the function used to encode ProtoBuf responses
	// If not set, google.golang.org/protobuf/proto.Marshal will be used
	ProtoBufEncoder func(v proto.Message) ([]byte, error)
}

// Engine is the main router engine
type Engine struct {
	gnet.BuiltinEventEngine
	router          *Router
	middleware      []Middleware
	addr            string
	multicore       bool
	eng             gnet.Engine
	jsonEncoder     func(v interface{}) ([]byte, error)
	jsonDecoder     func(data []byte, v interface{}) error
	xmlEncoder      func(v interface{}) ([]byte, error)
	yamlEncoder     func(v interface{}) ([]byte, error)
	protobufEncoder func(v proto.Message) ([]byte, error)
}

// Router handles route registration and matching
type Router struct {
	routes map[string]*routeNode // method -> root node
	mu     sync.RWMutex
}

// routeNode represents a node in the route tree
type routeNode struct {
	path        string      // path segment
	isParam     bool        // is this a parameter node (:param)
	isWildcard  bool        // is this a wildcard node (*param)
	paramKey    string      // parameter key name (for :id -> "id", *filepath -> "filepath")
	handler     HandlerFunc // handler for this exact path
	children    []*routeNode // child nodes
}

// RouteInfo contains route information
type RouteInfo struct {
	Method  string
	Path    string
	Handler HandlerFunc
}

// RouterGroup represents a group of routes with a common prefix and middleware
type RouterGroup struct {
	prefix     string
	middleware []Middleware
	engine     *Engine
}

// New creates a new Engine instance with default configuration
func New() *Engine {
	return NewWithConfig(Config{})
}

// NewWithConfig creates a new Engine instance with custom configuration
func NewWithConfig(config Config) *Engine {
	engine := &Engine{
		router: &Router{
			routes: make(map[string]*routeNode),
		},
		middleware: make([]Middleware, 0),
		multicore:  true,
	}

	// Set JSON encoder, use default if not provided
	if config.JSONEncoder != nil {
		engine.jsonEncoder = config.JSONEncoder
	} else {
		engine.jsonEncoder = json.Marshal
	}

	// Set JSON decoder, use default if not provided
	if config.JSONDecoder != nil {
		engine.jsonDecoder = config.JSONDecoder
	} else {
		engine.jsonDecoder = json.Unmarshal
	}

	// Set XML encoder, use default if not provided
	if config.XMLEncoder != nil {
		engine.xmlEncoder = config.XMLEncoder
	} else {
		engine.xmlEncoder = xml.Marshal
	}

	// Set YAML encoder, use default if not provided
	if config.YAMLEncoder != nil {
		engine.yamlEncoder = config.YAMLEncoder
	} else {
		engine.yamlEncoder = yaml.Marshal
	}

	// Set ProtoBuf encoder, use default if not provided
	if config.ProtoBufEncoder != nil {
		engine.protobufEncoder = config.ProtoBufEncoder
	} else {
		engine.protobufEncoder = proto.Marshal
	}

	return engine
}

// Default creates a new Engine with default middleware
func Default() *Engine {
	engine := New()
	engine.Use(Logger(), Recovery())
	return engine
}

// Use adds middleware to the engine
func (e *Engine) Use(middleware ...Middleware) {
	e.middleware = append(e.middleware, middleware...)
}

// GET registers a GET route
func (e *Engine) GET(path string, handler HandlerFunc) {
	e.addRoute("GET", path, handler)
}

// POST registers a POST route
func (e *Engine) POST(path string, handler HandlerFunc) {
	e.addRoute("POST", path, handler)
}

// PUT registers a PUT route
func (e *Engine) PUT(path string, handler HandlerFunc) {
	e.addRoute("PUT", path, handler)
}

// DELETE registers a DELETE route
func (e *Engine) DELETE(path string, handler HandlerFunc) {
	e.addRoute("DELETE", path, handler)
}

// PATCH registers a PATCH route
func (e *Engine) PATCH(path string, handler HandlerFunc) {
	e.addRoute("PATCH", path, handler)
}

// HEAD registers a HEAD route
func (e *Engine) HEAD(path string, handler HandlerFunc) {
	e.addRoute("HEAD", path, handler)
}

// OPTIONS registers an OPTIONS route
func (e *Engine) OPTIONS(path string, handler HandlerFunc) {
	e.addRoute("OPTIONS", path, handler)
}

// Group creates a new router group with the given path prefix
func (e *Engine) Group(prefix string) *RouterGroup {
	return &RouterGroup{
		prefix:     prefix,
		middleware: make([]Middleware, 0),
		engine:     e,
	}
}

// Static serves files from the given file system root
func (e *Engine) Static(relativePath, root string) {
	e.StaticFS(relativePath, http.Dir(root))
}

// StaticFS serves files from the given file system
func (e *Engine) StaticFS(relativePath string, fs http.FileSystem) {
	if strings.Contains(relativePath, ":") || strings.Contains(relativePath, "*") {
		panic("URL parameters can not be used when serving a static folder")
	}

	handler := e.createStaticHandler(relativePath, fs)

	// Ensure the path ends with /*filepath for wildcard matching
	urlPattern := relativePath
	if !strings.HasSuffix(urlPattern, "/") {
		urlPattern += "/"
	}
	urlPattern += "*filepath"

	// Register the route for serving static files
	e.GET(urlPattern, handler)
}

// StaticFile serves a single file
func (e *Engine) StaticFile(relativePath, filepath string) {
	if strings.Contains(relativePath, ":") || strings.Contains(relativePath, "*") {
		panic("URL parameters can not be used when serving a static file")
	}

	handler := func(c *Context) {
		c.File(filepath)
	}

	e.GET(relativePath, handler)
}

// addRoute adds a route to the router
func (e *Engine) addRoute(method, path string, handler HandlerFunc) {
	e.router.mu.Lock()
	defer e.router.mu.Unlock()

	if e.router.routes[method] == nil {
		e.router.routes[method] = &routeNode{}
	}

	e.insertRoute(e.router.routes[method], path, handler)
}

// insertRoute inserts a route into the route tree
func (e *Engine) insertRoute(root *routeNode, path string, handler HandlerFunc) {
	if path == "" || path == "/" {
		root.handler = handler
		return
	}

	// Remove leading slash
	if path[0] == '/' {
		path = path[1:]
	}

	segments := strings.Split(path, "/")
	current := root

	for _, segment := range segments {
		if segment == "" {
			continue
		}

		// Check if this is a parameter segment (:param) or wildcard (*param)
		isParam := strings.HasPrefix(segment, ":")
		isWildcard := strings.HasPrefix(segment, "*")
		paramKey := ""

		if isParam {
			paramKey = segment[1:] // Remove the ':' prefix
		} else if isWildcard {
			paramKey = segment[1:] // Remove the '*' prefix
		}

		// Find existing child node
		var child *routeNode
		for _, c := range current.children {
			if (isParam && c.isParam) || (isWildcard && c.isWildcard) ||
			   (!isParam && !isWildcard && !c.isParam && !c.isWildcard && c.path == segment) {
				child = c
				break
			}
		}

		// Create new child if not found
		if child == nil {
			child = &routeNode{
				path:       segment,
				isParam:    isParam,
				isWildcard: isWildcard,
				paramKey:   paramKey,
			}
			current.children = append(current.children, child)
		}

		current = child
	}

	current.handler = handler
}

// Run starts the HTTP server
func (e *Engine) Run(addr ...string) error {
	address := ":8080"
	if len(addr) > 0 {
		address = addr[0]
	}

	if !strings.HasPrefix(address, "tcp://") {
		address = "tcp://" + address
	}

	e.addr = address
	e.multicore = true

	log.Printf("Starting server on %s", address)
	return gnet.Run(e, address, gnet.WithMulticore(e.multicore))
}

// OnBoot is called when the server starts
func (e *Engine) OnBoot(eng gnet.Engine) gnet.Action {
	e.eng = eng
	log.Printf("HTTP server with multi-core=%t is listening on %s", e.multicore, e.addr)
	return gnet.None
}

// OnOpen is called when a new connection is opened
func (e *Engine) OnOpen(c gnet.Conn) ([]byte, gnet.Action) {
	c.SetContext(&httpCodec{parser: newHTTPParser()})
	return nil, gnet.None
}

// OnTraffic handles incoming HTTP requests
func (e *Engine) OnTraffic(c gnet.Conn) gnet.Action {
	hc := c.Context().(*httpCodec)
	buf, _ := c.Peek(-1)
	n := len(buf)

	for {
		nextOffset, req, err := hc.parse(buf)
		hc.resetParser()
		if err != nil || req == nil {
			break
		}
		if len(buf) < nextOffset {
			break
		}

		// Handle the request
		ctx := e.createContext(c, req)
		e.handleRequest(ctx)

		buf = buf[nextOffset:]
		if len(buf) == 0 {
			break
		}
	}

	if len(hc.buf) > 0 {
		c.Write(hc.buf)
	}
	hc.reset()
	c.Discard(n - len(buf))
	return gnet.None
}

// createContext creates a new context for the request
func (e *Engine) createContext(c gnet.Conn, req *http.Request) *Context {
	ctx := &Context{
		conn:     c,
		request:  req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params:   make(map[string]string),
		keys:     make(map[string]interface{}),
		handlers: make([]HandlerFunc, 0),
		index:    -1,
		engine:   e, // set engine reference
	}
	return ctx
}

// handleRequest handles the HTTP request
func (e *Engine) handleRequest(ctx *Context) {
	// Find the handler and extract parameters
	handler, params := e.findHandlerWithParams(ctx.request.Method, ctx.request.URL.Path)
	if handler == nil {
		ctx.Status(404).String("Not Found")
		return
	}

	// Set the extracted parameters
	for key, value := range params {
		ctx.params[key] = value
	}

	// Apply middleware
	ctx.handlers = append(ctx.handlers, handler)
	for i := len(e.middleware) - 1; i >= 0; i-- {
		handler = e.middleware[i](handler)
	}

	// Execute the handler
	handler(ctx)
}

// findHandler finds the handler for the given method and path
func (e *Engine) findHandler(method, path string) HandlerFunc {
	e.router.mu.RLock()
	defer e.router.mu.RUnlock()

	root, ok := e.router.routes[method]
	if !ok {
		return nil
	}

	handler, _ := e.searchRoute(root, path)
	return handler
}

// findHandlerWithParams finds the handler and extracts parameters
func (e *Engine) findHandlerWithParams(method, path string) (HandlerFunc, map[string]string) {
	e.router.mu.RLock()
	defer e.router.mu.RUnlock()

	root, ok := e.router.routes[method]
	if !ok {
		return nil, nil
	}

	return e.searchRoute(root, path)
}

// searchRoute searches for a route in the tree and extracts parameters
func (e *Engine) searchRoute(root *routeNode, path string) (HandlerFunc, map[string]string) {
	if path == "" || path == "/" {
		return root.handler, make(map[string]string)
	}

	// Remove leading slash
	if path[0] == '/' {
		path = path[1:]
	}

	segments := strings.Split(path, "/")
	params := make(map[string]string)

	return e.matchRoute(root, segments, 0, params)
}

// matchRoute recursively matches route segments
func (e *Engine) matchRoute(node *routeNode, segments []string, index int, params map[string]string) (HandlerFunc, map[string]string) {
	// If we've matched all segments
	if index == len(segments) {
		return node.handler, params
	}

	segment := segments[index]
	if segment == "" {
		return e.matchRoute(node, segments, index+1, params)
	}

	// Try to match child nodes
	for _, child := range node.children {
		if child.isWildcard {
			// Wildcard node matches all remaining segments
			remainingPath := strings.Join(segments[index:], "/")
			params[child.paramKey] = remainingPath
			return child.handler, params
		} else if child.isParam {
			// Parameter node matches any segment
			params[child.paramKey] = segment
			if handler, matchedParams := e.matchRoute(child, segments, index+1, params); handler != nil {
				return handler, matchedParams
			}
			// Remove the parameter if no match found
			delete(params, child.paramKey)
		} else if child.path == segment {
			// Exact match
			if handler, matchedParams := e.matchRoute(child, segments, index+1, params); handler != nil {
				return handler, matchedParams
			}
		}
	}

	return nil, nil
}

// Context methods

// Param returns the value of the URL parameter
func (c *Context) Param(key string) string {
	return c.params[key]
}

// Query returns the query parameter value
func (c *Context) Query(key string) string {
	return c.request.URL.Query().Get(key)
}

// DefaultQuery returns the query parameter value or default value
func (c *Context) DefaultQuery(key, defaultValue string) string {
	if value := c.Query(key); value != "" {
		return value
	}
	return defaultValue
}

// PostForm returns the form parameter value
func (c *Context) PostForm(key string) string {
	return c.request.FormValue(key)
}

// DefaultPostForm returns the form parameter value or default value
func (c *Context) DefaultPostForm(key, defaultValue string) string {
	if value := c.PostForm(key); value != "" {
		return value
	}
	return defaultValue
}

// GetHeader returns the request header value
func (c *Context) GetHeader(key string) string {
	return c.request.Header.Get(key)
}

// Request returns the underlying http.Request
func (c *Context) Request() *http.Request {
	return c.request
}

// Cookie returns the named cookie provided in the request
func (c *Context) Cookie(name string) (string, error) {
	cookie, err := c.request.Cookie(name)
	if err != nil {
		return "", err
	}
	return cookie.Value, nil
}

// SetCookie adds a Set-Cookie header to the ResponseWriter's headers
func (c *Context) SetCookie(name, value string, maxAge int, path, domain string, secure, httpOnly bool) {
	if path == "" {
		path = "/"
	}

	cookie := &http.Cookie{
		Name:     name,
		Value:    value,
		MaxAge:   maxAge,
		Path:     path,
		Domain:   domain,
		Secure:   secure,
		HttpOnly: httpOnly,
	}

	c.Header("Set-Cookie", cookie.String())
}

// Header sets the response header
func (c *Context) Header(key, value string) {
	c.response.headers[key] = value
}

// Status sets the response status code
func (c *Context) Status(code int) *Context {
	c.response.statusCode = code
	return c
}

// Set stores a key-value pair in the context
func (c *Context) Set(key string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.keys[key] = value
}

// Get retrieves a value from the context
func (c *Context) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	value, exists := c.keys[key]
	return value, exists
}

// MustGet retrieves a value from the context or panics
func (c *Context) MustGet(key string) interface{} {
	if value, exists := c.Get(key); exists {
		return value
	}
	panic("Key \"" + key + "\" does not exist")
}

// String sends a string response
func (c *Context) String(format string, values ...interface{}) {
	c.Header("Content-Type", "text/plain; charset=utf-8")
	body := fmt.Sprintf(format, values...)
	c.response.body = []byte(body)
	c.writeResponse()
}

// JSON sends a JSON response
func (c *Context) JSON(obj interface{}) {
	c.Header("Content-Type", "application/json; charset=utf-8")
	body, err := c.engine.jsonEncoder(obj)
	if err != nil {
		c.Status(500).String("Internal Server Error")
		return
	}
	c.response.body = body
	c.writeResponse()
}

// XML sends an XML response
func (c *Context) XML(obj interface{}) {
	c.Header("Content-Type", "application/xml; charset=utf-8")
	body, err := c.engine.xmlEncoder(obj)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}
	c.response.body = body
	c.writeResponse()
}

// YAML sends a YAML response
func (c *Context) YAML(obj interface{}) {
	c.Header("Content-Type", "application/x-yaml; charset=utf-8")
	body, err := c.engine.yamlEncoder(obj)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}
	c.response.body = body
	c.writeResponse()
}

// ProtoBuf sends a Protocol Buffer response
func (c *Context) ProtoBuf(obj proto.Message) {
	c.Header("Content-Type", "application/x-protobuf")
	body, err := c.engine.protobufEncoder(obj)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}
	c.response.body = body
	c.writeResponse()
}

// HTML sends an HTML response
func (c *Context) HTML(html string) {
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.response.body = []byte(html)
	c.writeResponse()
}

// Data sends raw data response
func (c *Context) Data(contentType string, data []byte) {
	c.Header("Content-Type", contentType)
	c.response.body = data
	c.writeResponse()
}

// DataFromReader sends data from a reader with optional extra headers
func (c *Context) DataFromReader(code int, contentLength int64, contentType string, reader io.Reader, extraHeaders map[string]string) {
	c.Status(code)
	c.Header("Content-Type", contentType)

	// Set extra headers
	for key, value := range extraHeaders {
		c.Header(key, value)
	}

	// Read data from reader
	data, err := io.ReadAll(reader)
	if err != nil {
		c.Status(500)
		c.response.body = []byte("Internal Server Error")
		c.writeResponse()
		return
	}

	c.response.body = data
	c.writeResponse()
}

// File sends a file response
func (c *Context) File(filepath string) {
	// Check if file exists
	info, err := os.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			c.Status(404).String("Not Found")
		} else {
			c.Status(500).String("Internal Server Error")
		}
		return
	}

	// Check if it's a directory
	if info.IsDir() {
		c.Status(403).String("Forbidden")
		return
	}

	// Read the file
	data, err := os.ReadFile(filepath)
	if err != nil {
		c.Status(500).String("Internal Server Error")
		return
	}

	// Detect content type
	contentType := mime.TypeByExtension(path.Ext(filepath))
	if contentType == "" {
		contentType = http.DetectContentType(data)
	}

	// Set headers
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", fmt.Sprintf("%d", len(data)))

	// Set cache headers
	c.Header("Cache-Control", "public, max-age=31536000")
	c.Header("Last-Modified", info.ModTime().UTC().Format(http.TimeFormat))

	// Send the file
	c.response.body = data
	c.writeResponse()
}

// Redirect sends a redirect response
func (c *Context) Redirect(code int, location string) {
	c.Header("Location", location)
	c.Status(code)
	c.writeResponse()
}

// writeResponse writes the response to the connection
func (c *Context) writeResponse() {
	if c.response.written {
		return
	}
	c.response.written = true

	// For testing, if conn is nil, just return
	if c.conn == nil {
		return
	}

	hc, ok := c.conn.Context().(*httpCodec)
	if !ok || hc == nil {
		return
	}

	// Status line
	hc.buf = append(hc.buf, fmt.Sprintf("HTTP/1.1 %d %s\r\n", c.response.statusCode, http.StatusText(c.response.statusCode))...)

	// Headers
	hc.buf = append(hc.buf, "Server: gnet-mux\r\n"...)
	hc.buf = append(hc.buf, "Date: "...)
	hc.buf = time.Now().AppendFormat(hc.buf, "Mon, 02 Jan 2006 15:04:05 GMT")
	hc.buf = append(hc.buf, "\r\n"...)

	for key, value := range c.response.headers {
		hc.buf = append(hc.buf, key...)
		hc.buf = append(hc.buf, ": "...)
		hc.buf = append(hc.buf, value...)
		hc.buf = append(hc.buf, "\r\n"...)
	}

	// Content-Length
	hc.buf = append(hc.buf, "Content-Length: "...)
	hc.buf = append(hc.buf, strconv.Itoa(len(c.response.body))...)
	hc.buf = append(hc.buf, "\r\n\r\n"...)

	// Body
	hc.buf = append(hc.buf, c.response.body...)
}

// Next executes the next handler in the chain
func (c *Context) Next() {
	c.index++
	if c.index < len(c.handlers) {
		c.handlers[c.index](c)
	}
}

// Abort prevents pending handlers from being called
func (c *Context) Abort() {
	c.index = len(c.handlers)
}

// AbortWithStatus aborts with a status code
func (c *Context) AbortWithStatus(code int) {
	c.Status(code)
	c.Abort()
}

// AbortWithStatusJSON aborts with a JSON response
func (c *Context) AbortWithStatusJSON(code int, obj interface{}) {
	c.Status(code)
	c.JSON(obj)
	c.Abort()
}

// Middleware implementations

// Logger returns a logger middleware
func Logger() Middleware {
	return func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			start := time.Now()
			path := c.request.URL.Path
			raw := c.request.URL.RawQuery

			next(c)

			latency := time.Since(start)
			clientIP := c.request.RemoteAddr
			method := c.request.Method
			statusCode := c.response.statusCode

			if raw != "" {
				path = path + "?" + raw
			}

			log.Printf("[%s] %s %s %d %v", clientIP, method, path, statusCode, latency)
		}
	}
}

// Recovery returns a recovery middleware
func Recovery() Middleware {
	return func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			defer func() {
				if err := recover(); err != nil {
					log.Printf("Panic recovered: %v", err)
					c.AbortWithStatus(500)
				}
			}()
			next(c)
		}
	}
}

// CORS returns a CORS middleware
func CORS() Middleware {
	return func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			c.Header("Access-Control-Allow-Origin", "*")
			c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

			if c.request.Method == "OPTIONS" {
				c.Status(200)
				c.writeResponse()
				return
			}

			next(c)
		}
	}
}

// RouterGroup methods

// Use adds middleware to the router group
func (rg *RouterGroup) Use(middleware ...Middleware) *RouterGroup {
	rg.middleware = append(rg.middleware, middleware...)
	return rg
}

// Group creates a new nested router group with the given path prefix
func (rg *RouterGroup) Group(prefix string) *RouterGroup {
	return &RouterGroup{
		prefix:     rg.prefix + prefix,
		middleware: make([]Middleware, 0),
		engine:     rg.engine,
	}
}

// GET registers a GET route in the router group
func (rg *RouterGroup) GET(path string, handler HandlerFunc) {
	rg.addRoute("GET", path, handler)
}

// POST registers a POST route in the router group
func (rg *RouterGroup) POST(path string, handler HandlerFunc) {
	rg.addRoute("POST", path, handler)
}

// PUT registers a PUT route in the router group
func (rg *RouterGroup) PUT(path string, handler HandlerFunc) {
	rg.addRoute("PUT", path, handler)
}

// DELETE registers a DELETE route in the router group
func (rg *RouterGroup) DELETE(path string, handler HandlerFunc) {
	rg.addRoute("DELETE", path, handler)
}

// PATCH registers a PATCH route in the router group
func (rg *RouterGroup) PATCH(path string, handler HandlerFunc) {
	rg.addRoute("PATCH", path, handler)
}

// HEAD registers a HEAD route in the router group
func (rg *RouterGroup) HEAD(path string, handler HandlerFunc) {
	rg.addRoute("HEAD", path, handler)
}

// OPTIONS registers an OPTIONS route in the router group
func (rg *RouterGroup) OPTIONS(path string, handler HandlerFunc) {
	rg.addRoute("OPTIONS", path, handler)
}

// addRoute adds a route to the router group with the group's prefix and middleware
func (rg *RouterGroup) addRoute(method, path string, handler HandlerFunc) {
	// Combine group prefix with route path
	fullPath := rg.prefix + path

	// Wrap handler with group middleware
	finalHandler := handler
	for i := len(rg.middleware) - 1; i >= 0; i-- {
		finalHandler = rg.middleware[i](finalHandler)
	}

	// Register the route with the engine
	rg.engine.addRoute(method, fullPath, finalHandler)
}

// Static file serving helper functions

// createStaticHandler creates a handler for serving static files
func (e *Engine) createStaticHandler(relativePath string, fs http.FileSystem) HandlerFunc {
	absolutePath := relativePath
	if relativePath != "/" {
		absolutePath = "/" + strings.Trim(relativePath, "/")
	}

	fileServer := http.StripPrefix(absolutePath, http.FileServer(fs))

	return func(c *Context) {
		file := c.Param("filepath")

		// Security check: prevent directory traversal
		if strings.Contains(file, "..") {
			c.Status(403).String("Forbidden")
			return
		}

		// Try to open the file to check if it exists
		f, err := fs.Open(file)
		if err != nil {
			c.Status(404).String("Not Found")
			return
		}
		defer f.Close()

		// Check if it's a directory
		stat, err := f.Stat()
		if err != nil {
			c.Status(404).String("Not Found")
			return
		}

		if stat.IsDir() {
			// Try to serve index.html
			indexFile := path.Join(file, "index.html")
			indexF, err := fs.Open(indexFile)
			if err != nil {
				c.Status(403).String("Forbidden")
				return
			}
			indexF.Close()
			file = indexFile
		}

		// Serve the file using http.FileServer
		c.request.URL.Path = absolutePath + "/" + file
		fileServer.ServeHTTP(&responseWriter{c}, c.request)
	}
}

// responseWriter implements http.ResponseWriter for compatibility with http.FileServer
type responseWriter struct {
	ctx *Context
}

func (w *responseWriter) Header() http.Header {
	// Convert our headers to http.Header format
	header := make(http.Header)
	for k, v := range w.ctx.response.headers {
		header.Set(k, v)
	}
	return header
}

func (w *responseWriter) Write(data []byte) (int, error) {
	w.ctx.response.body = append(w.ctx.response.body, data...)
	w.ctx.writeResponse()
	return len(data), nil
}

func (w *responseWriter) WriteHeader(statusCode int) {
	w.ctx.Status(statusCode)
}
