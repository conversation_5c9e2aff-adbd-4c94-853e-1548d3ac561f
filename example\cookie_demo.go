//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

func main() {
	router := mux.Default()

	// Cookie example (following Gin example exactly)
	router.GET("/cookie", func(c *mux.Context) {
		cookie, err := c.<PERSON>("gin_cookie")

		if err != nil {
			cookie = "NotSet"
			c.Set<PERSON><PERSON>ie("gin_cookie", "test", 3600, "/", "localhost", false, true)
		}

		fmt.Printf("Cookie value: %s \n", cookie)

		// Send response showing cookie value
		c.JSON(mux.H{
			"message":      "Cookie demo",
			"cookie_value": cookie,
			"status":       "success",
		})
	})

	// Additional cookie examples
	router.GET("/set-cookie", func(c *mux.Context) {
		// Set various types of cookies
		c.SetCookie("session_id", "abc123", 3600, "/", "", false, true)
		c.<PERSON><PERSON><PERSON>ie("user_pref", "dark_mode", 86400, "/", "", false, false)
		c.<PERSON>("secure_token", "xyz789", 1800, "/admin", "localhost", true, true)

		c.<PERSON><PERSON><PERSON>(mux.H{
			"message": "Cookies set successfully",
			"cookies": []string{
				"session_id (HttpOnly, 1 hour)",
				"user_pref (1 day)",
				"secure_token (Secure, HttpOnly, 30 minutes, /admin path)",
			},
		})
	})

	router.GET("/get-cookies", func(c *mux.Context) {
		cookies := make(map[string]string)

		// Try to get various cookies
		cookieNames := []string{"gin_cookie", "session_id", "user_pref", "secure_token"}

		for _, name := range cookieNames {
			if value, err := c.Cookie(name); err == nil {
				cookies[name] = value
			} else {
				cookies[name] = "Not found"
			}
		}

		c.JSON(mux.H{
			"message": "Retrieved cookies",
			"cookies": cookies,
		})
	})

	router.GET("/delete-cookie", func(c *mux.Context) {
		cookieName := c.DefaultQuery("name", "gin_cookie")

		// Delete cookie by setting MaxAge to -1
		c.SetCookie(cookieName, "", -1, "/", "", false, false)

		c.JSON(mux.H{
			"message":     "Cookie deleted",
			"cookie_name": cookieName,
		})
	})

	// Cookie-based session example
	router.GET("/login", func(c *mux.Context) {
		username := c.DefaultQuery("username", "guest")

		// Set session cookie
		c.SetCookie("session_user", username, 3600, "/", "", false, true)

		c.JSON(mux.H{
			"message":  "Logged in successfully",
			"username": username,
		})
	})

	router.GET("/profile", func(c *mux.Context) {
		username, err := c.Cookie("session_user")

		if err != nil {
			c.JSON(mux.H{
				"error":   "Not logged in",
				"message": "Please login first",
			})
			return
		}

		c.JSON(mux.H{
			"message":  "User profile",
			"username": username,
			"profile": mux.H{
				"name":  username,
				"role":  "user",
				"email": username + "@example.com",
			},
		})
	})

	router.GET("/logout", func(c *mux.Context) {
		// Delete session cookie
		c.SetCookie("session_user", "", -1, "/", "", false, true)

		c.JSON(mux.H{
			"message": "Logged out successfully",
		})
	})

	// Root endpoint with instructions
	router.GET("/", func(c *mux.Context) {
		c.JSON(mux.H{
			"message": "Cookie Demo API",
			"endpoints": []string{
				"GET /cookie - Gin-style cookie example",
				"GET /set-cookie - Set multiple cookies",
				"GET /get-cookies - Get all cookies",
				"GET /delete-cookie?name=<cookie_name> - Delete a cookie",
				"GET /login?username=<name> - Login with session cookie",
				"GET /profile - Get user profile (requires login)",
				"GET /logout - Logout and clear session",
			},
			"instructions": []string{
				"1. Visit /cookie to see the Gin example in action",
				"2. Visit /set-cookie to set some cookies",
				"3. Visit /get-cookies to see all cookies",
				"4. Try the login flow: /login?username=john -> /profile -> /logout",
			},
		})
	})

	fmt.Println("Server starting on :8080")
	fmt.Println("Try these endpoints:")
	fmt.Println("  GET / - API overview")
	fmt.Println("  GET /cookie - Gin-style cookie example")
	fmt.Println("  GET /set-cookie - Set multiple cookies")
	fmt.Println("  GET /get-cookies - Get all cookies")
	fmt.Println("  GET /login?username=john - Login example")
	fmt.Println("  GET /profile - Profile (requires login)")
	fmt.Println("")
	fmt.Println("Example commands:")
	fmt.Println("  curl http://localhost:8080/cookie")
	fmt.Println("  curl http://localhost:8080/set-cookie")
	fmt.Println("  curl -c cookies.txt http://localhost:8080/login?username=john")
	fmt.Println("  curl -b cookies.txt http://localhost:8080/profile")

	log.Fatal(router.Run(":8080"))
}
