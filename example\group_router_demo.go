//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

// Mock handlers for demonstration
func loginEndpoint(c *mux.Context) {
	version := c.<PERSON>eader("API-Version")
	if version == "" {
		version = "unknown"
	}

	c.<PERSON>(mux.H{
		"message": "Login endpoint",
		"version": version,
		"path":    c.Request().URL.Path,
	})
}

func submitEndpoint(c *mux.Context) {
	version := c.GetHeader("API-Version")
	if version == "" {
		version = "unknown"
	}

	c.JSON(mux.H{
		"message": "Submit endpoint",
		"version": version,
		"path":    c.Request().URL.Path,
	})
}

func readEndpoint(c *mux.Context) {
	version := c.GetHeader("API-Version")
	if version == "" {
		version = "unknown"
	}

	c.<PERSON>(mux.H{
		"message": "Read endpoint",
		"version": version,
		"path":    c.Request().URL.Path,
	})
}

// Middleware to add API version header
func APIVersionMiddleware(version string) mux.Middleware {
	return func(next mux.HandlerFunc) mux.HandlerFunc {
		return func(c *mux.Context) {
			c.Header("API-Version", version)
			next(c)
		}
	}
}

// Middleware to add authentication check
func AuthMiddleware() mux.Middleware {
	return func(next mux.HandlerFunc) mux.HandlerFunc {
		return func(c *mux.Context) {
			token := c.GetHeader("Authorization")
			if token == "" {
				c.Status(http.StatusUnauthorized).JSON(mux.H{
					"error": "Authorization header required",
				})
				return
			}

			// Mock token validation
			if token != "Bearer valid-token" {
				c.Status(http.StatusUnauthorized).JSON(mux.H{
					"error": "Invalid token",
				})
				return
			}

			c.Set("user_id", "123")
			next(c)
		}
	}
}

func main() {
	router := mux.Default()

	// Root level routes
	router.GET("/", func(c *mux.Context) {
		c.JSON(mux.H{
			"message": "Welcome to the API",
			"endpoints": []string{
				"GET /",
				"GET /health",
				"POST /v1/login",
				"POST /v1/submit",
				"POST /v1/read",
				"POST /v2/login",
				"POST /v2/submit",
				"POST /v2/read",
				"GET /api/v1/users/:id",
				"POST /api/v1/users",
				"GET /api/v2/users/:id",
				"POST /api/v2/users",
			},
		})
	})

	router.GET("/health", func(c *mux.Context) {
		c.JSON(mux.H{"status": "healthy"})
	})

	// Simple group: v1 (following Gin example exactly)
	{
		v1 := router.Group("/v1")
		v1.Use(APIVersionMiddleware("v1.0"))
		v1.POST("/login", loginEndpoint)
		v1.POST("/submit", submitEndpoint)
		v1.POST("/read", readEndpoint)
	}

	// Simple group: v2 (following Gin example exactly)
	{
		v2 := router.Group("/v2")
		v2.Use(APIVersionMiddleware("v2.0"))
		v2.POST("/login", loginEndpoint)
		v2.POST("/submit", submitEndpoint)
		v2.POST("/read", readEndpoint)
	}

	// More complex nested groups with middleware
	api := router.Group("/api")
	api.Use(func(next mux.HandlerFunc) mux.HandlerFunc {
		return func(c *mux.Context) {
			c.Header("X-API-Gateway", "gnet-mux")
			next(c)
		}
	})

	// API v1 group with authentication
	{
		v1 := api.Group("/v1")
		v1.Use(APIVersionMiddleware("v1.0"))
		v1.Use(AuthMiddleware())

		v1.GET("/users/:id", func(c *mux.Context) {
			userID := c.Param("id")
			currentUser := c.MustGet("user_id").(string)

			c.JSON(mux.H{
				"user_id":      userID,
				"current_user": currentUser,
				"version":      "v1.0",
				"message":      "User details from API v1",
			})
		})

		v1.POST("/users", func(c *mux.Context) {
			currentUser := c.MustGet("user_id").(string)

			c.JSON(mux.H{
				"message":      "User created in API v1",
				"created_by":   currentUser,
				"version":      "v1.0",
			})
		})
	}

	// API v2 group with different authentication
	{
		v2 := api.Group("/v2")
		v2.Use(APIVersionMiddleware("v2.0"))
		v2.Use(AuthMiddleware())

		v2.GET("/users/:id", func(c *mux.Context) {
			userID := c.Param("id")
			currentUser := c.MustGet("user_id").(string)

			c.JSON(mux.H{
				"user_id":      userID,
				"current_user": currentUser,
				"version":      "v2.0",
				"message":      "User details from API v2 with enhanced features",
				"features":     []string{"caching", "rate-limiting", "analytics"},
			})
		})

		v2.POST("/users", func(c *mux.Context) {
			currentUser := c.MustGet("user_id").(string)

			c.JSON(mux.H{
				"message":      "User created in API v2",
				"created_by":   currentUser,
				"version":      "v2.0",
				"enhanced":     true,
			})
		})
	}

	// Nested groups example
	admin := api.Group("/admin")
	admin.Use(func(next mux.HandlerFunc) mux.HandlerFunc {
		return func(c *mux.Context) {
			c.Header("X-Admin-Access", "true")
			next(c)
		}
	})

	{
		v1 := admin.Group("/v1")
		v1.GET("/stats", func(c *mux.Context) {
			c.JSON(mux.H{
				"message": "Admin stats v1",
				"path":    c.Request().URL.Path,
			})
		})
	}

	fmt.Println("Server starting on :8080")
	fmt.Println("Try these endpoints:")
	fmt.Println("  GET / - API overview")
	fmt.Println("  GET /health - Health check")
	fmt.Println("  POST /v1/login - V1 login (no auth required)")
	fmt.Println("  POST /v2/login - V2 login (no auth required)")
	fmt.Println("  GET /api/v1/users/123 - V1 user details (requires: Authorization: Bearer valid-token)")
	fmt.Println("  GET /api/v2/users/123 - V2 user details (requires: Authorization: Bearer valid-token)")
	fmt.Println("  GET /api/admin/v1/stats - Admin stats")

	log.Fatal(router.Run(":8080"))
}
